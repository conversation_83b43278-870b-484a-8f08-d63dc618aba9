<?php

use App\Models\Trip;
use App\Models\Company;
use App\Models\Driver;
use App\Models\Vehicle;
use App\Providers\Filament\AppPanelProvider;
use Filament\Support\Facades\FilamentView;
use Filament\View\PanelsRenderHook;
use Illuminate\Support\Facades\Cache;

beforeEach(function () {
    $this->company = Company::factory()->create();
    $this->driver = Driver::factory()->create(['company_id' => $this->company->id]);
    $this->vehicle = Vehicle::factory()->create(['company_id' => $this->company->id]);
});

describe('Topbar Active Trips Stats', function () {
    it('can render active trips stats view', function () {
        $activeTripsCount = 5;

        $html = view('filament.topbar.active-trips-stats', [
            'activeTripsCount' => $activeTripsCount
        ])->render();

        expect($html)->toContain('Active Trips');
        expect($html)->toContain('5');
        expect($html)->toContain('w-5 h-5'); // Check for icon classes instead
    });

    it('shows correct active trips count', function () {
        // Create some trips
        Trip::factory()->active()->create([
            'company_id' => $this->company->id,
            'driver_id' => $this->driver->id,
            'vehicle_id' => $this->vehicle->id,
        ]);

        Trip::factory()->completed()->create([
            'company_id' => $this->company->id,
            'driver_id' => $this->driver->id,
            'vehicle_id' => $this->vehicle->id,
        ]);

        $activeCount = Trip::ongoing()->count();
        expect($activeCount)->toBe(1);

        $html = view('filament.topbar.active-trips-stats', [
            'activeTripsCount' => $activeCount
        ])->render();

        expect($html)->toContain('1');
    });

    it('can access render active trips stats method', function () {
        $provider = new AppPanelProvider(app());
        $method = new ReflectionMethod($provider, 'renderActiveTripsStats');
        $method->setAccessible(true);

        $result = $method->invoke($provider);

        expect($result)->toBeString();
        expect($result)->toContain('Active Trips');
    });
});
